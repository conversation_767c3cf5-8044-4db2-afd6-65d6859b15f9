#!/usr/bin/env python3
# run_system_simple.py - Script di avvio semplice per il sistema CMS
import subprocess
import sys
import os
import time
import signal
from pathlib import Path

def initialize_bobina_vuota():
    """Inizializza il record BOBINA_VUOTA nella tabella parco_cavi se non esiste già"""
    print("🔧 Verifica e inizializzazione del record BOBINA_VUOTA...")

    # Importa i moduli necessari
    sys.path.insert(0, str(Path(__file__).resolve().parent.parent))
    try:
        from scripts.add_bobina_vuota import add_bobina_vuota

        # Esegui la funzione di inizializzazione
        result = add_bobina_vuota()
        if result:
            print("✅ Record BOBINA_VUOTA verificato con successo.")
        else:
            print("⚠️ Errore durante l'inizializzazione del record BOBINA_VUOTA.")
    except Exception as e:
        print(f"⚠️ Errore durante l'inizializzazione del record BOBINA_VUOTA: {e}")

def fix_bobina_vuota():
    """Corregge i cavi non posati che hanno erroneamente id_bobina = 'BOBINA_VUOTA'."""
    print("🔧 Correzione dei cavi non posati con id_bobina errato...")

    # Importa i moduli necessari
    sys.path.insert(0, str(Path(__file__).resolve().parent.parent))
    try:
        from scripts.fix_bobina_vuota import fix_bobina_vuota as fix_function

        # Esegui la funzione di correzione
        result = fix_function()
        if result:
            print("✅ Correzione dei cavi completata con successo.")
        else:
            print("⚠️ Errore durante la correzione dei cavi.")
    except Exception as e:
        print(f"⚠️ Errore durante la correzione dei cavi: {e}")

def kill_process_on_port(port):
    """Termina il processo che usa una specifica porta"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if f':{port}' in line and 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            try:
                                pid = int(pid)
                                print(f"🔪 Terminazione processo PID {pid} sulla porta {port}")
                                subprocess.run(['taskkill', '/F', '/PID', str(pid)], capture_output=True)
                                time.sleep(2)  # Attendi che il processo si chiuda
                                return True
                            except ValueError:
                                pass
        return False
    except Exception as e:
        print(f"⚠️ Errore durante la terminazione processo sulla porta {port}: {e}")
        return False

def run_backend():
    """Avvia il server FastAPI (backend)"""
    print("🚀 Avvio del backend...")

    # Ottieni il percorso assoluto della directory backend
    backend_dir = Path(__file__).resolve().parent / "backend"

    # Verifica che la directory esista
    if not backend_dir.exists():
        print(f"❌ La directory del backend non esiste: {backend_dir}")
        return None

    # Cambia directory
    original_dir = os.getcwd()
    os.chdir(backend_dir)

    # Comando per avviare il backend
    cmd = [sys.executable, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]
    print(f"📝 Esecuzione comando: {' '.join(cmd)}")

    try:
        # Avvia il processo
        process = subprocess.Popen(cmd)

        # Attendi un po' per assicurarsi che il server si avvii
        time.sleep(5)

        print("✅ Backend avviato con successo!")
        return process
    except Exception as e:
        print(f"❌ Errore durante l'avvio del backend: {e}")
        return None
    finally:
        # Torna alla directory originale
        os.chdir(original_dir)

def kill_process_on_port(port):
    """Termina il processo che usa una specifica porta"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if f':{port}' in line and 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            try:
                                pid = int(pid)
                                print(f"🔪 Terminazione processo PID {pid} sulla porta {port}")
                                subprocess.run(['taskkill', '/F', '/PID', str(pid)], capture_output=True)
                                time.sleep(2)  # Attendi che il processo si chiuda
                                return True
                            except ValueError:
                                pass
        return False
    except Exception as e:
        print(f"⚠️ Errore durante la terminazione processo sulla porta {port}: {e}")
        return False

def run_frontend():
    """Avvia il server React (frontend)"""
    print("🚀 Avvio del frontend...")

    # Prima libera la porta 3000 se occupata
    print("🔍 Verifica porta 3000...")
    if kill_process_on_port(3000):
        print("✅ Porta 3000 liberata")

    # Ottieni il percorso assoluto della directory frontend
    frontend_dir = Path(__file__).resolve().parent / "frontend"

    # Verifica che la directory esista
    if not frontend_dir.exists():
        print(f"❌ La directory del frontend non esiste: {frontend_dir}")
        return None

    # Cambia directory
    original_dir = os.getcwd()
    os.chdir(frontend_dir)

    # Comando per avviare il frontend
    if os.name == 'nt':  # Windows
        cmd = ["cmd", "/c", "npm", "start"]
    else:  # Linux/Mac
        cmd = ["npm", "start"]

    print(f"📝 Esecuzione comando: {' '.join(cmd)}")

    try:
        # Imposta variabili d'ambiente per React
        env = os.environ.copy()
        env['REACT_APP_API_URL'] = 'http://localhost:8001/api'
        env['BROWSER'] = 'none'  # Non aprire automaticamente il browser
        env['PORT'] = '3000'  # Forza l'uso della porta 3000

        # Avvia il processo
        process = subprocess.Popen(cmd, env=env, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)

        # Attendi un po' per assicurarsi che il server si avvii
        time.sleep(10)

        # Verifica che il processo sia ancora in esecuzione
        if process.poll() is None:
            print("✅ Frontend avviato con successo!")
            return process
        else:
            print("❌ Il frontend si è chiuso inaspettatamente")
            # Leggi l'output per debug
            try:
                output, _ = process.communicate(timeout=1)
                if output:
                    print(f"📝 Output frontend: {output[:200]}...")
            except:
                pass
            return None
    except Exception as e:
        print(f"❌ Errore durante l'avvio del frontend: {e}")
        return None
    finally:
        # Torna alla directory originale
        os.chdir(original_dir)

def main():
    """Funzione principale"""
    print("\n🚀 === AVVIO SISTEMA CMS ===\n")

    # Salva la directory corrente
    original_dir = os.getcwd()

    try:
        # Avvia il backend
        backend_process = run_backend()
        if not backend_process:
            print("❌ Impossibile avviare il backend.")
            return False

        # Avvia il frontend
        frontend_process = run_frontend()
        if not frontend_process:
            print("❌ Impossibile avviare il frontend.")
            # Termina il backend
            backend_process.terminate()
            return False

        # Sistema avviato con successo
        print("\n🎉 === SISTEMA CMS AVVIATO CON SUCCESSO! ===")
        print("🌐 Backend API: http://localhost:8001")
        print("🌐 Frontend: http://localhost:3000")
        print("📚 Documentazione API: http://localhost:8001/docs")
        print("⚡ Premi Ctrl+C per terminare entrambi i server\n")

        # Gestione del segnale di interruzione
        def signal_handler(sig, frame):
            print("\n🛑 Terminazione dei server in corso...")
            try:
                if frontend_process and frontend_process.poll() is None:
                    frontend_process.terminate()
                    print("✅ Frontend terminato")
                if backend_process and backend_process.poll() is None:
                    backend_process.terminate()
                    print("✅ Backend terminato")
            except Exception as e:
                print(f"❌ Errore durante la terminazione: {e}")

            print("👋 Sistema CMS terminato. Arrivederci!")
            sys.exit(0)

        # Registra il gestore del segnale
        signal.signal(signal.SIGINT, signal_handler)

        # Mantiene il programma in esecuzione
        try:
            while True:
                # Verifica che i processi siano ancora in esecuzione
                if backend_process.poll() is not None:
                    print("❌ Il backend si è chiuso inaspettatamente")
                    break
                if frontend_process.poll() is not None:
                    print("❌ Il frontend si è chiuso inaspettatamente")
                    break
                time.sleep(2)
        except KeyboardInterrupt:
            # Questo blocco non dovrebbe essere mai raggiunto grazie al signal_handler
            pass

    finally:
        # Torna alla directory originale
        os.chdir(original_dir)

    return True

if __name__ == "__main__":
    main()
